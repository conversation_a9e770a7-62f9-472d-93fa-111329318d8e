package org.dromara.app.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.dromara.app.controller.system.QuestionCommentController;
import org.dromara.app.domain.bo.QuestionCommentBo;
import org.dromara.app.domain.vo.QuestionCommentVO;
import org.dromara.app.service.IQuestionCommentService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 题目评论Controller测试类
 *
 * <AUTHOR>
 */
@WebMvcTest(QuestionCommentController.class)
class QuestionCommentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IQuestionCommentService questionCommentService;

    @Autowired
    private ObjectMapper objectMapper;

    private QuestionCommentVO mockCommentVO;
    private QuestionCommentBo mockCommentBo;

    @BeforeEach
    void setUp() {
        mockCommentVO = new QuestionCommentVO();
        mockCommentVO.setId("1");
        mockCommentVO.setQuestionId("1");
        mockCommentVO.setUserId("1");
        mockCommentVO.setContent("测试评论内容");
        mockCommentVO.setLikes(5);
        mockCommentVO.setReplyCount(2);

        mockCommentBo = new QuestionCommentBo();
        mockCommentBo.setCommentId(1L);
        mockCommentBo.setQuestionId(1L);
        mockCommentBo.setUserId(1L);
        mockCommentBo.setContent("测试评论内容");
    }

    @Test
    void testGetCommentList() throws Exception {
        // Given
        List<QuestionCommentVO> comments = Arrays.asList(mockCommentVO);
        TableDataInfo<QuestionCommentVO> tableData = TableDataInfo.build(comments);
        when(questionCommentService.queryPageList(any(QuestionCommentBo.class), any(PageQuery.class)))
            .thenReturn(tableData);

        // When & Then
        mockMvc.perform(get("/system/questioncomment/list")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.rows").isArray())
                .andExpect(jsonPath("$.data.rows[0].id").value("1"))
                .andExpect(jsonPath("$.data.rows[0].content").value("测试评论内容"));
    }

    @Test
    void testGetCommentDetail() throws Exception {
        // Given
        when(questionCommentService.queryById(1L)).thenReturn(mockCommentVO);

        // When & Then
        mockMvc.perform(get("/system/questioncomment/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data.id").value("1"))
                .andExpected(jsonPath("$.data.content").value("测试评论内容"));
    }

    @Test
    void testAddComment() throws Exception {
        // Given
        when(questionCommentService.insertByBo(any(QuestionCommentBo.class))).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/system/questioncomment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockCommentBo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testEditComment() throws Exception {
        // Given
        when(questionCommentService.updateByBo(any(QuestionCommentBo.class))).thenReturn(true);

        // When & Then
        mockMvc.perform(put("/system/questioncomment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockCommentBo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testDeleteComment() throws Exception {
        // Given
        when(questionCommentService.deleteWithValidByIds(anyList())).thenReturn(true);

        // When & Then
        mockMvc.perform(delete("/system/questioncomment/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testGetQuestionComments() throws Exception {
        // Given
        Map<String, Object> result = new HashMap<>();
        result.put("comments", Arrays.asList(mockCommentVO));
        result.put("total", 1);
        result.put("page", 1);
        result.put("pageSize", 10);
        result.put("hasMore", false);
        
        when(questionCommentService.getQuestionComments("1", 1, 10, "createTime", "desc"))
            .thenReturn(result);

        // When & Then
        mockMvc.perform(get("/system/questioncomment/question/1")
                .param("page", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.comments").isArray())
                .andExpect(jsonPath("$.data.total").value(1));
    }

    @Test
    void testSearchComments() throws Exception {
        // Given
        List<QuestionCommentVO> comments = Arrays.asList(mockCommentVO);
        TableDataInfo<QuestionCommentVO> tableData = TableDataInfo.build(comments);
        when(questionCommentService.searchComments(eq("测试"), any(PageQuery.class)))
            .thenReturn(tableData);

        // When & Then
        mockMvc.perform(get("/system/questioncomment/search")
                .param("keyword", "测试")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.rows").isArray());
    }

    @Test
    void testGetStatistics() throws Exception {
        // Given
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalComments", 10L);
        statistics.put("mainComments", 7L);
        statistics.put("replies", 3L);
        
        when(questionCommentService.getCommentStatistics("1")).thenReturn(statistics);

        // When & Then
        mockMvc.perform(get("/system/questioncomment/statistics/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalComments").value(10))
                .andExpect(jsonPath("$.data.mainComments").value(7))
                .andExpect(jsonPath("$.data.replies").value(3));
    }

    @Test
    void testAuditComment() throws Exception {
        // Given
        when(questionCommentService.auditComment(eq(1L), eq("1"), eq(1L), eq("审核通过")))
            .thenReturn(true);

        Map<String, Object> auditRequest = new HashMap<>();
        auditRequest.put("status", "1");
        auditRequest.put("reason", "审核通过");

        // When & Then
        mockMvc.perform(post("/system/questioncomment/1/audit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(auditRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testToggleTop() throws Exception {
        // Given
        when(questionCommentService.toggleCommentTop(eq(1L), eq(true), eq(1L)))
            .thenReturn(true);

        Map<String, Object> topRequest = new HashMap<>();
        topRequest.put("isTop", true);

        // When & Then
        mockMvc.perform(post("/system/questioncomment/1/toggle-top")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(topRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testBatchDelete() throws Exception {
        // Given
        List<Long> commentIds = Arrays.asList(1L, 2L, 3L);
        when(questionCommentService.batchDeleteComments(eq(commentIds), eq(1L)))
            .thenReturn(true);

        // When & Then
        mockMvc.perform(post("/system/questioncomment/batch-delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(commentIds)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testGetReplies() throws Exception {
        // Given
        List<QuestionCommentVO> replies = Arrays.asList(mockCommentVO);
        when(questionCommentService.getRepliesByParentId(1L)).thenReturn(replies);

        // When & Then
        mockMvc.perform(get("/system/questioncomment/1/replies"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value("1"));
    }

    @Test
    void testInvalidCommentId() throws Exception {
        // When & Then
        mockMvc.perform(get("/system/questioncomment/invalid"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testEmptyRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/system/questioncomment")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isBadRequest());
    }
}
