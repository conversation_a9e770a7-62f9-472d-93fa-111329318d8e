{"info": {"name": "问题管理CRUD功能测试", "description": "SmartInterview系统问题管理功能的完整API测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "{{auth_token}}", "type": "string"}], "item": [{"name": "1. 基础CRUD测试", "item": [{"name": "查询评论列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/system/questioncomment/list?pageNum=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "list"], "query": [{"key": "pageNum", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "获取评论详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/system/questioncomment/1", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "1"]}}, "response": []}, {"name": "新增评论", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"questionId\": 1,\n  \"userId\": 1,\n  \"content\": \"这是一个测试评论\",\n  \"parentId\": null\n}"}, "url": {"raw": "{{baseUrl}}/system/questioncomment", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment"]}}, "response": []}, {"name": "修改评论", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"commentId\": 1,\n  \"content\": \"修改后的评论内容\"\n}"}, "url": {"raw": "{{baseUrl}}/system/questioncomment", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment"]}}, "response": []}, {"name": "删除评论", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/system/questioncomment/1", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "1"]}}, "response": []}]}, {"name": "2. 高级功能测试", "item": [{"name": "获取题目评论（含回复）", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/system/questioncomment/question/1?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "question", "1"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "搜索评论", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/system/questioncomment/search?keyword=测试&pageNum=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "search"], "query": [{"key": "keyword", "value": "测试"}, {"key": "pageNum", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "评论统计", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/system/questioncomment/statistics/1", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "statistics", "1"]}}, "response": []}, {"name": "导出评论", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"questionId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/system/questioncomment/export", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "export"]}}, "response": []}]}, {"name": "3. 管理功能测试", "item": [{"name": "审核评论", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"1\",\n  \"reason\": \"审核通过\"\n}"}, "url": {"raw": "{{baseUrl}}/system/questioncomment/1/audit", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "1", "audit"]}}, "response": []}, {"name": "置顶评论", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"isTop\": true\n}"}, "url": {"raw": "{{baseUrl}}/system/questioncomment/1/toggle-top", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "1", "toggle-top"]}}, "response": []}, {"name": "批量删除评论", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[1, 2, 3]"}, "url": {"raw": "{{baseUrl}}/system/questioncomment/batch-delete", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "batch-delete"]}}, "response": []}]}, {"name": "4. 异常处理测试", "item": [{"name": "无效评论ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/system/questioncomment/999999", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment", "999999"]}}, "response": []}, {"name": "空评论内容", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"questionId\": 1,\n  \"userId\": 1,\n  \"content\": \"\"\n}"}, "url": {"raw": "{{baseUrl}}/system/questioncomment", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment"]}}, "response": []}, {"name": "超长评论内容", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"questionId\": 1,\n  \"userId\": 1,\n  \"content\": \"这是一个超过1000字符的评论内容测试...(重复到1001字符)\"\n}"}, "url": {"raw": "{{baseUrl}}/system/questioncomment", "host": ["{{baseUrl}}"], "path": ["system", "questioncomment"]}}, "response": []}]}]}