{"doc": " 题库收藏Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectBookmarksByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题库列表\n\n @param userId 用户ID\n @return 收藏的题库列表\n"}, {"name": "selectBookmarkByUserAndBank", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 查询用户是否收藏了指定题库\n\n @param userId 用户ID\n @param bankId 题库ID\n @return 收藏记录\n"}, {"name": "countBookmarksByBankId", "paramTypes": ["java.lang.Long"], "doc": " 查询题库的收藏数量\n\n @param bankId 题库ID\n @return 收藏数量\n"}, {"name": "countBookmarksByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题库数量\n\n @param userId 用户ID\n @return 收藏数量\n"}, {"name": "insertOrUpdateBookmark", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String"], "doc": " 添加或更新收藏状态\n\n @param userId 用户ID\n @param bankId 题库ID\n @param status 收藏状态\n @return 操作结果\n"}, {"name": "cancelBookmark", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 取消收藏（软删除）\n\n @param userId 用户ID\n @param bankId 题库ID\n @return 操作结果\n"}, {"name": "batchCancelBookmarks", "paramTypes": ["java.lang.Long", "java.util.List"], "doc": " 批量取消收藏\n\n @param userId  用户ID\n @param bankIds 题库ID列表\n @return 操作结果\n"}, {"name": "clearUserBookmarks", "paramTypes": ["java.lang.Long"], "doc": " 清理用户的所有收藏\n\n @param userId 用户ID\n @return 操作结果\n"}], "constructors": []}